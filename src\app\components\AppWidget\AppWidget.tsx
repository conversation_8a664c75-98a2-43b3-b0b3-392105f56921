"use client";
import { IoSettingsOutline } from "react-icons/io5";

export default function AppWidget({ url, title , description, color, icon}: { url: string; title: string; description: string; color?: string; icon?: any }) {
  const handleWidgetClick = (url: string) => {
    window.location.href = url;
  };
  return (
    <div
        className="group bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-lg border border-white/10 rounded-3xl p-8 cursor-pointer transition-all duration-500 ease-out w-80 shadow-2xl hover:-translate-y-3 hover:shadow-purple-500/25 hover:border-purple-400/50 relative overflow-hidden"
        onClick={() => handleWidgetClick(url)}
        >
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
        <div className="relative z-10">
            <div className="w-16 h-16 bg-gradient-to-br from-[#667eea] to-purple-500 rounded-2xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
                {icon ? icon : <IoSettingsOutline size={28}/>}
            </div>
            <h2 className="text-2xl mb-3 font-semibold group-hover:text-blue-300 transition-colors duration-300">{title}</h2>
            <p className="text-base opacity-80 group-hover:opacity-100 transition-opacity duration-300">{description}</p>
        </div>
    </div>

  );
}
