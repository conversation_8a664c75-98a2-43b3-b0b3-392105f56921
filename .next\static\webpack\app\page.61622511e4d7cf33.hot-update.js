"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/components/AppWidget/AppWidget.tsx":
/*!****************************************************!*\
  !*** ./src/app/components/AppWidget/AppWidget.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppWidget)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_IoSettingsOutline_react_icons_io5__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=IoSettingsOutline!=!react-icons/io5 */ \"(app-pages-browser)/./node_modules/react-icons/io5/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AppWidget(param) {\n    let { url, title, description, color, icon } = param;\n    const handleWidgetClick = (url)=>{\n        window.location.href = url;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"group bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-lg border border-white/10 rounded-3xl p-8 cursor-pointer transition-all duration-500 ease-out w-80 shadow-2xl hover:-translate-y-3 hover:shadow-purple-500/25 hover:border-purple-400/50 relative overflow-hidden\",\n        onClick: ()=>handleWidgetClick(url),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n            }, void 0, false, {\n                fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\components\\\\AppWidget\\\\AppWidget.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-br from-[#667eea] to-purple-500 rounded-2xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300\",\n                        children: icon ? icon : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IoSettingsOutline_react_icons_io5__WEBPACK_IMPORTED_MODULE_1__.IoSettingsOutline, {\n                            size: 28\n                        }, void 0, false, {\n                            fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\components\\\\AppWidget\\\\AppWidget.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 32\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\components\\\\AppWidget\\\\AppWidget.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl mb-3 font-semibold group-hover:text-blue-300 transition-colors duration-300\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\components\\\\AppWidget\\\\AppWidget.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-base opacity-80 group-hover:opacity-100 transition-opacity duration-300\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\components\\\\AppWidget\\\\AppWidget.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\components\\\\AppWidget\\\\AppWidget.tsx\",\n                lineNumber: 14,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\components\\\\AppWidget\\\\AppWidget.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n_c = AppWidget;\nvar _c;\n$RefreshReg$(_c, \"AppWidget\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/AppWidget/AppWidget.tsx\n"));

/***/ })

});