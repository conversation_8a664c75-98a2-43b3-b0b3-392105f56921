"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_AppWidget_AppWidget__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/AppWidget/AppWidget */ \"(app-pages-browser)/./src/app/components/AppWidget/AppWidget.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Home() {\n    _s();\n    const [dateTime, setDateTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Loading time and weather...');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const updateDateTime = {\n                \"Home.useEffect.updateDateTime\": ()=>{\n                    const now = new Date();\n                    const options = {\n                        weekday: 'long',\n                        year: 'numeric',\n                        month: 'long',\n                        day: 'numeric'\n                    };\n                    const timeStr = now.toLocaleTimeString('vi-VN');\n                    const dateStr = now.toLocaleDateString('vi-VN', options);\n                    // Placeholder weather (replace with real API if needed)\n                    const weather = \"Nhiệt độ: 30°C - Trời nắng\";\n                    setDateTime(\"\".concat(dateStr, \" - \").concat(timeStr));\n                }\n            }[\"Home.useEffect.updateDateTime\"];\n            updateDateTime();\n            const interval = setInterval(updateDateTime, 1000);\n            return ({\n                \"Home.useEffect\": ()=>clearInterval(interval)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], []);\n    const handleWidgetClick = (url)=>{\n        window.location.href = url;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white flex flex-col items-center justify-center p-5 text-center font-sans relative overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative z-10\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"flex flex-col items-center mb-12 animate-fade-in-up\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-6 mb-6 group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/logo/logo.png\",\n                                        alt: \"Company Logo\",\n                                        width: 80,\n                                        height: 40,\n                                        className: \"w-full transition-transform duration-300 group-hover:scale-110 group-hover:rotate-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-[#667eea] to-purple-400 rounded-full blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl mb-2 bg-gradient-to-r from-[#667eea] via-purple-400 to-pink-400 bg-clip-text text-transparent font-bold\",\n                            children: \"Ch\\xe0o mừng đến với hệ thống th\\xf4ng tin FTI\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg text-slate-300 mb-8 backdrop-blur-sm bg-white/5 px-6 py-2 rounded-full border border-white/10\",\n                            children: dateTime\n                        }, void 0, false, {\n                            fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-6xl mb-12 font-bold bg-gradient-to-r from-[#667eea] via-purple-400 to-pink-400 bg-clip-text text-transparent animate-pulse-slow\",\n                    children: \"FTI Information System\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-8 flex-wrap justify-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppWidget_AppWidget__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            title: \"FTI Booking\",\n                            description: \"Hệ thống booking\",\n                            url: \"https://domain1.example.com\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 10\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppWidget_AppWidget__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            title: \"FTI Agent AI\",\n                            description: \"Hệ thống Agent AI\",\n                            url: \"https://domain2.example.com\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"0itSD3v12ShXa+qUNdDxOBFjklA=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});