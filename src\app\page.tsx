'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import AppWidget from './components/AppWidget/AppWidget';

export default function Home() {
  const [dateTime, setDateTime] = useState('Loading time and weather...');

  useEffect(() => {
    const updateDateTime = () => {
      const now = new Date();
      const options: Intl.DateTimeFormatOptions = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      };
      const timeStr = now.toLocaleTimeString('vi-VN');
      const dateStr = now.toLocaleDateString('vi-VN', options);

      // Placeholder weather (replace with real API if needed)
      const weather = "Nhiệt độ: 30°C - Trời nắng";

      setDateTime(`${dateStr} - ${timeStr}`);
    };

    updateDateTime();
    const interval = setInterval(updateDateTime, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleWidgetClick = (url: string) => {
    window.location.href = url;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white flex flex-col items-center justify-center p-5 text-center font-sans relative overflow-hidden">
      {/* Animated background elements */}
      {/* <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
        <div className="absolute top-40 left-1/2 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
      </div> */}

      {/* Floating particles */}
      {/* <div className="absolute inset-0">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-white rounded-full opacity-20 animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${3 + Math.random() * 4}s`
            }}
          ></div>
        ))}
      </div> */}

      <div className="relative z-10">
        <header className="flex flex-col items-center mb-12 animate-fade-in-up">
          <div className="flex items-center gap-6 mb-6 group">
            <div className="relative">
              <Image
                src="/logo/logo.png"
                alt="Company Logo"
                width={80}
                height={40}
                className="w-full transition-transform duration-300 group-hover:scale-110 group-hover:rotate-3"
              />
              <div className="absolute inset-0 bg-gradient-to-r from-[#667eea] to-purple-400 rounded-full blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-300"></div>
            </div>
          </div>
          <div className="text-2xl mb-2 bg-gradient-to-r from-[#667eea] via-purple-400 to-pink-400 bg-clip-text text-transparent font-bold">
            Chào mừng đến với hệ thống thông tin FTI
          </div>
          <div className="text-lg text-slate-300 mb-8 backdrop-blur-sm bg-white/5 px-6 py-2 rounded-full border border-white/10">
            {dateTime}
          </div>
        </header>

        <h1 className="text-6xl mb-12 font-bold bg-gradient-to-r from-[#667eea] via-purple-400 to-pink-400 bg-clip-text text-transparent animate-pulse-slow">
          FTI Information System
        </h1>

        <div className="flex gap-8 flex-wrap justify-center mb-8">
         <AppWidget title='FTI Booking' description='Hệ thống booking' url='https://domain1.example.com' />

          <div
            className="group bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-lg border border-white/10 rounded-3xl p-8 cursor-pointer transition-all duration-500 ease-out w-80 shadow-2xl hover:-translate-y-3 hover:shadow-pink-500/25 hover:border-pink-400/50 relative overflow-hidden"
            onClick={() => handleWidgetClick('https://domain2.example.com')}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="relative z-10">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h2 className="text-2xl mb-3 font-semibold group-hover:text-pink-300 transition-colors duration-300">FTI Agent AI</h2>
              <p className="text-base opacity-80 group-hover:opacity-100 transition-opacity duration-300">Hệ thống Agent AI</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
