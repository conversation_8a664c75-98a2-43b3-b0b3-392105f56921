"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/components/AppWidget/AppWidget.tsx":
/*!****************************************************!*\
  !*** ./src/app/components/AppWidget/AppWidget.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppWidget)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_IoSettingsOutline_react_icons_io5__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=IoSettingsOutline!=!react-icons/io5 */ \"(app-pages-browser)/./node_modules/react-icons/io5/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AppWidget(param) {\n    let { url, title, description, color, icon } = param;\n    const handleWidgetClick = (url)=>{\n        window.location.href = url;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"group bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-lg border border-white/10 rounded-3xl p-8 cursor-pointer transition-all duration-500 ease-out w-80 shadow-2xl hover:-translate-y-3 hover:shadow-purple-500/25 hover:border-purple-400/50 relative overflow-hidden\",\n        onClick: ()=>handleWidgetClick(url),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n            }, void 0, false, {\n                fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\components\\\\AppWidget\\\\AppWidget.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-br from-[#667eea] to-purple-500 rounded-2xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300\",\n                        children: icon ? icon : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IoSettingsOutline_react_icons_io5__WEBPACK_IMPORTED_MODULE_1__.IoSettingsOutline, {\n                            size: 28\n                        }, void 0, false, {\n                            fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\components\\\\AppWidget\\\\AppWidget.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 32\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\components\\\\AppWidget\\\\AppWidget.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl mb-3 font-semibold group-hover:text-blue-300 transition-colors duration-300\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\components\\\\AppWidget\\\\AppWidget.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-base opacity-80 group-hover:opacity-100 transition-opacity duration-300\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\components\\\\AppWidget\\\\AppWidget.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\components\\\\AppWidget\\\\AppWidget.tsx\",\n                lineNumber: 14,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\fti\\\\fti-landing-web\\\\src\\\\app\\\\components\\\\AppWidget\\\\AppWidget.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n_c = AppWidget;\nvar _c;\n$RefreshReg$(_c, \"AppWidget\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/AppWidget/AppWidget.tsx\n"));

/***/ })

});